/* Modern Split Design - Legal Theme */
@import '../styles/variables.css';

.login-container {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-medium-blue) 100%);
  font-family: var(--font-family-primary);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-normal);
}

/* خلفية هندسية متحركة */
.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(218, 209, 222, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(76, 104, 192, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(218, 209, 222, 0.05) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(2deg); }
}

/* الجانب الأيسر - الخلفية والرسوم الإيضاحية */
.left-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 40px;
  background: linear-gradient(145deg, var(--primary-dark-blue) 0%, var(--dark-blue-gray) 100%);
}

.legal-graphics {
  position: relative;
  z-index: 2;
  text-align: center;
  color: var(--white);
}

.legal-icons-group {
  margin-bottom: 30px;
  position: relative;
}

.legal-icon.main-icon {
  font-size: 120px;
  color: var(--light-purple-gray);
  opacity: 0.9;
  animation: pulse 3s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  display: block;
  margin-bottom: 20px;
}

.mini-icons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.mini-icon {
  font-size: 24px;
  opacity: 0.7;
  animation: float 4s ease-in-out infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.mini-icon:nth-child(1) { animation-delay: 0s; }
.mini-icon:nth-child(2) { animation-delay: 1s; }
.mini-icon:nth-child(3) { animation-delay: 2s; }

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.9; }
  50% { transform: scale(1.05); opacity: 1; }
}

.legal-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--white);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.legal-subtitle {
  font-size: 1.2rem;
  font-weight: 300;
  color: var(--light-purple-gray);
  margin-bottom: 30px;
  line-height: 1.6;
}

.highlight-text {
  color: var(--white);
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.feature-list {
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.feature-item {
  font-size: 1rem;
  color: var(--light-purple-gray);
  opacity: 0.8;
  padding: 10px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.feature-item:hover {
  opacity: 1;
  transform: translateX(-5px);
  background: rgba(255, 255, 255, 0.15);
}

.decorative-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  overflow: hidden;
}

.decorative-shape {
  position: absolute;
  border: 2px solid var(--light-purple-gray);
  border-radius: 50%;
  animation: rotate 30s linear infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: 15%;
  animation-delay: -10s;
}

.shape-3 {
  width: 100px;
  height: 100px;
  top: 60%;
  right: 20%;
  animation-delay: -20s;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* الجانب الأيمن - نموذج تسجيل الدخول */
.right-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--current-bg-primary);
  position: relative;
  transition: all var(--transition-normal);
}

.login-box {
  background: var(--current-bg-primary);
  padding: 60px 50px;
  border-radius: 20px;
  box-shadow: var(--current-shadow-heavy);
  width: 100%;
  max-width: 450px;
  position: relative;
  z-index: 2;
  border: 1px solid var(--current-border-primary);
  backdrop-filter: var(--current-backdrop-blur);
  transition: all var(--transition-normal);
}

.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.legal-agenda-logo {
  width: 180px;
  height: auto;
  margin-bottom: 25px;
  filter: drop-shadow(0 4px 8px var(--shadow-light));
}

.login-title {
  color: var(--current-text-primary);
  margin-bottom: 8px;
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1.2;
  font-family: var(--font-family-primary);
}

.login-subtitle {
  color: var(--current-text-secondary);
  font-size: 1rem;
  font-weight: 400;
  margin-bottom: 35px;
  line-height: 1.4;
}

.form-section {
  width: 100%;
}

.input-container {
  position: relative;
  min-height: 80px;
  margin-bottom: 25px;
}

.form-group {
  margin-bottom: 0;
  position: relative;
}

.form-group input[type='email'],
.form-group input[type='password'],
.single-input {
  /* الحفاظ على التصميم الأصلي مع استخدام المتغيرات الموحدة */
  width: 100%;
  padding: 20px 25px;
  border: 2px solid var(--current-border-primary);
  border-radius: 15px;
  background-color: var(--current-bg-secondary);
  color: var(--current-text-primary);
  font-size: 1.1rem;
  font-family: var(--font-family-primary);
  transition: var(--input-transition);
  box-sizing: border-box;
  outline: none;
  min-height: 65px;
  font-family: var(--font-family-primary);
  font-weight: 500;
  backdrop-filter: var(--current-backdrop-blur);
}

.form-group input:focus {
  /* الحفاظ على التأثير الأصلي */
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  transform: var(--transform-hover-up);
  outline: none;
}

.form-group input::placeholder {
  color: var(--current-text-tertiary);
  opacity: 0.7;
}

.form-group input:hover {
  border-color: var(--primary-color);
  box-shadow: var(--current-shadow-light);
}

.forgot-password {
  text-align: center;
  margin-bottom: 30px;
}

.password-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.forgot-password a,
.back-to-email a {
  color: var(--primary-medium-blue);
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.forgot-password a:hover,
.back-to-email a:hover {
  color: var(--primary-dark-blue);
  text-decoration: underline;
}

.back-to-email a {
  color: var(--dark-blue-gray);
  font-size: 0.9rem;
}

.button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
}

.create-account-link {
  color: var(--primary-medium-blue);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 10px 20px;
  border-radius: 10px;
}

.create-account-link:hover {
  background-color: var(--light-purple-gray);
  color: var(--primary-dark-blue);
}

.login-btn {
  /* الحفاظ على التصميم الأصلي مع استخدام المتغيرات الموحدة */
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  padding: 18px 40px;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: var(--btn-transition);
  min-height: 60px;
  width: 100%;
  font-family: var(--font-family-primary);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--btn-gap);
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:hover {
  /* الحفاظ على التأثير الأصلي */
  transform: var(--transform-hover-up);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.login-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px var(--shadow-medium);
}

.login-btn:disabled {
  background: var(--light-purple-gray);
  color: var(--dark-blue-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.error-message {
  color: #e53e3e;
  margin: 20px 0;
  padding: 15px 20px;
  background: linear-gradient(135deg, #fed7d7 0%, #fec2c2 100%);
  border-radius: 12px;
  border: 1px solid #fed7d7;
  font-size: 0.95rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.1);
}

.error-message::before {
  content: "⚠️";
  margin-left: 10px;
  font-size: 1.1rem;
}

.success-message {
  color: #38a169;
  margin: 20px 0;
  padding: 15px 20px;
  background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
  border-radius: 12px;
  border: 1px solid #c6f6d5;
  font-size: 0.95rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.1);
}

.success-message::before {
  content: "✅";
  margin-left: 10px;
  font-size: 1.1rem;
}

.loading-container {
  margin: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-purple-gray);
  border-top: 4px solid var(--primary-medium-blue);
  border-radius: 50%;
  animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--dark-blue-gray);
  font-size: 1rem;
  font-weight: 500;
}

.footer-links {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 20px;
  font-size: 0.85rem;
}

.footer-links a {
  color: var(--light-purple-gray);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.footer-links a:hover {
  color: var(--white);
  opacity: 1;
}

/* تأثيرات للأجهزة اللوحية */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
  }
  
  .left-section {
    flex: none;
    min-height: 40vh;
    padding: 30px 20px;
  }
  
  .legal-icon {
    font-size: 80px;
    margin-bottom: 20px;
  }
  
  .legal-title {
    font-size: 2rem;
    margin-bottom: 15px;
  }
  
  .legal-subtitle {
    font-size: 1rem;
    margin-bottom: 20px;
  }
  
  .right-section {
    flex: none;
    min-height: 60vh;
    padding: 20px;
  }
  
  .login-box {
    max-width: 500px;
    padding: 40px 30px;
  }
  
  .decorative-elements {
    display: none;
  }
  
  .footer-links {
    position: relative;
    bottom: auto;
    right: auto;
    margin-top: 20px;
    justify-content: center;
  }
}

/* تأثيرات للهواتف */
@media (max-width: 768px) {
  .left-section {
    min-height: 35vh;
    padding: 20px 15px;
  }
  
  .legal-icon.main-icon {
    font-size: 60px;
    margin-bottom: 15px;
  }
  
  .mini-icons {
    gap: 15px;
  }
  
  .mini-icon {
    font-size: 18px;
  }
  
  .feature-list {
    margin-top: 25px;
    gap: 10px;
  }
  
  .feature-item {
    font-size: 0.9rem;
    padding: 8px 15px;
  }
  
  .legal-title {
    font-size: 1.6rem;
    margin-bottom: 10px;
  }
  
  .legal-subtitle {
    font-size: 0.9rem;
    margin-bottom: 15px;
  }
  
  .login-box {
    padding: 30px 25px;
    margin: 0 10px;
    max-width: none;
  }
  
  .login-title {
    font-size: 1.8rem;
  }
  
  .login-subtitle {
    font-size: 0.9rem;
    margin-bottom: 25px;
  }
  
  .form-group input[type='email'],
  .form-group input[type='password'],
  .single-input {
    padding: 18px 20px;
    font-size: 1rem;
    min-height: 60px;
  }
  
  .password-actions {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .left-section {
    min-height: 30vh;
    padding: 15px 10px;
  }
  
  .legal-icon.main-icon {
    font-size: 50px;
    margin-bottom: 10px;
  }
  
  .mini-icons {
    gap: 10px;
    margin-top: 10px;
  }
  
  .mini-icon {
    font-size: 16px;
  }
  
  .feature-list {
    margin-top: 20px;
    gap: 8px;
  }
  
  .feature-item {
    font-size: 0.8rem;
    padding: 6px 12px;
    text-align: center;
  }
  
  .legal-title {
    font-size: 1.4rem;
    margin-bottom: 8px;
  }
  
  .legal-subtitle {
    font-size: 0.8rem;
    margin-bottom: 10px;
  }
  
  .login-box {
    padding: 25px 20px;
    margin: 0 5px;
    border-radius: 15px;
  }
  
  .legal-agenda-logo {
    width: 140px;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
  
  .login-subtitle {
    font-size: 0.85rem;
  }
  
  .form-group input[type='email'],
  .form-group input[type='password'],
  .single-input {
    padding: 16px 18px;
    font-size: 16px; /* يمنع التكبير في iOS */
    min-height: 55px;
  }
  
  .login-btn {
    padding: 16px 30px;
    font-size: 1rem;
  }
}

/* تحسينات إضافية */
.login-box:focus-within {
  box-shadow: 
    0 25px 70px var(--shadow-medium),
    0 15px 35px var(--shadow-light);
}

.form-group input:focus {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
  outline: none;
}

.login-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.3);
}

/* تأثيرات smooth للانتقالات */
* {
  box-sizing: border-box;
}

.login-container * {
  transition: all 0.3s ease;
}