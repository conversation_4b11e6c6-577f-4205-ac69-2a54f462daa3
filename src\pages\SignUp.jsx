import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import './SignUp.css';
import { auth, db } from '../config/firebaseConfig';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth";
import { collection, doc, setDoc } from "firebase/firestore";
import { checkSignupLockout, recordSignupFailure, clearSignupLockout } from '../utils/LockoutManager';

// إضافة الثوابت المفقودة
const MAX_ATTEMPTS = 5;
const LOCKOUT_DURATION = 15 * 60 * 1000; // 15 دقيقة

function SignUp() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [phone, setPhone] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [signupAttempts, setSignupAttempts] = useState(0);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState([]);
  const navigate = useNavigate();

  useEffect(() => {
    const lockoutStatus = checkSignupLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(true);
    } else {
      setSignupAttempts(lockoutStatus.attempts);
    }
  }, []);

  // دوال التعامل مع الخطوات
  const nextStep = () => {
    if (currentStep < 4) {
      setCompletedSteps([...completedSteps, currentStep]);
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = (step) => {
    switch (step) {
      case 1:
        return username.trim().length > 0;
      case 2:
        return email.trim().length > 0 && email.includes('@');
      case 3:
        return password.trim().length >= 6;
      case 4:
        return true; // الهاتف اختياري
      default:
        return false;
    }
  };

  const handleStepSubmit = (e) => {
    e.preventDefault();
    if (currentStep === 4) {
      handleSubmit(e);
    } else if (isStepValid(currentStep)) {
      nextStep();
    } else {
      setErrorMessage(getStepErrorMessage(currentStep));
    }
  };

  const getStepErrorMessage = (step) => {
    switch (step) {
      case 1:
        return 'يرجى إدخال اسم المستخدم.';
      case 2:
        return 'يرجى إدخال بريد إلكتروني صحيح.';
      case 3:
        return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل.';
      default:
        return '';
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setErrorMessage('');
    setSuccessMessage('');
    setLoading(true);

    if (!email.trim()) {
      setErrorMessage('يرجى إدخال البريد الإلكتروني.');
      setLoading(false);
      return;
    }

    if (!password.trim()) {
      setErrorMessage('يرجى إدخال كلمة المرور.');
      setLoading(false);
      return;
    }

    // التحقق من حالة القفل قبل المحاولة
    const lockoutStatus = checkSignupLockout();
    if (lockoutStatus.isLocked) {
      setErrorMessage(lockoutStatus.message);
      setLoading(false);
      return;
    }

    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email.trim(), password);
      const user = userCredential.user;

      console.log('تم إنشاء حساب بنجاح:', user);

      if (username.trim()) {
        await updateProfile(user, { displayName: username.trim() });
        console.log('تم تحديث اسم المستخدم:', username.trim());
      }

      await setDoc(doc(db, 'users', user.uid), {
        name: username.trim() || '',
        username: username.trim() || null,
        email: user.email,
        phone: phone.trim() || null,
        role: 'محامي',
        createdAt: new Date().toISOString(),
      });

      console.log('تم حفظ بيانات المستخدم في Firestore');

      // مسح بيانات القفل عند النجاح
      clearSignupLockout();
      setSignupAttempts(0);
      setSuccessMessage('تم إنشاء الحساب بنجاح! سيتم توجيهك إلى صفحة تسجيل الدخول.');

      setTimeout(() => {
        navigate('/login');
      }, 1500);

    } catch (error) {
      console.error('خطأ في إنشاء الحساب:', error.code, error.message);

      // تسجيل المحاولة الفاشلة
      const failureResult = recordSignupFailure(signupAttempts);
      setSignupAttempts(failureResult.attempts);

      if (failureResult.isLocked) {
        setErrorMessage(failureResult.message);
        setLoading(true);
        return;
      }

      let userFacingMessage = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
      switch (error.code) {
        case 'auth/email-already-in-use':
          userFacingMessage = 'البريد الإلكتروني هذا مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر أو تسجيل الدخول.';
          break;
        case 'auth/invalid-email':
          userFacingMessage = 'صيغة البريد الإلكتروني غير صحيحة. يرجى إدخال بريد إلكتروني صالح.';
          break;
        case 'auth/weak-password':
          userFacingMessage = 'كلمة المرور ضعيفة جدًا. يجب أن تحتوي على 6 أحرف على الأقل.';
          break;
        case 'auth/too-many-requests':
          userFacingMessage = 'تم حظر إنشاء الحساب مؤقتًا بسبب كثرة المحاولات. يرجى المحاولة لاحقًا.';
          break;
        default:
          userFacingMessage = 'حدث خطأ غير متوقع. يرجى التحقق من اتصال الإنترنت والمحاولة لاحقًا.';
      }
      setErrorMessage(userFacingMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/login');
  };

  return (
    <div className="signup-container">
      <div className="signup-box">
        <div className="signup-content">
          <div className="logo-section">
            <img
              src="/logo.png"
              alt="الأجندة القضائية"
              className="legal-agenda-logo"
            />
          </div>
          
          <div className="form-section">
            {/* مؤشر الخطوات */}
            <div className="steps-indicator">
              {[1, 2, 3, 4].map((step) => (
                <div 
                  key={step}
                  className={`step-dot ${
                    completedSteps.includes(step) ? 'completed' : 
                    currentStep === step ? 'active' : 'pending'
                  }`}
                >
                  {completedSteps.includes(step) ? '✓' : step}
                </div>
              ))}
            </div>

            {errorMessage && <div className="error-message">{errorMessage}</div>}
            {successMessage && <div className="success-message">{successMessage}</div>}
            
            {loading && <LoadingSpinner message="جاري إنشاء الحساب..." />}

            <div className="form-content">
              <form id="signup-form" onSubmit={handleStepSubmit}>
                <div className="header-form">
                  <span className="title">مرحباً بك!</span>
                  <span className="subtitle">اختر خياراً للمتابعة</span>
                </div>

                {/* الخطوة الأولى: اسم المستخدم */}
                {currentStep === 1 && (
                  <div className="step-container active">
                    <h3 className="step-title">الخطوة 1: اسم المستخدم</h3>
                    <input
                      type="text"
                      id="signup-username"
                      name="username"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      disabled={loading}
                      placeholder="اسم المستخدم"
                      autoComplete="username"
                      autoFocus
                      required={true}
                    />
                  </div>
                )}

                {/* الخطوة الثانية: البريد الإلكتروني */}
                {currentStep === 2 && (
                  <div className="step-container active">
                    <h3 className="step-title">الخطوة 2: البريد الإلكتروني</h3>
                    <input
                      type="email"
                      id="signup-email"
                      name="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required={true}
                      disabled={loading}
                      placeholder="البريد الإلكتروني"
                      autoComplete="email"
                      autoFocus
                    />
                  </div>
                )}

                {/* الخطوة الثالثة: كلمة المرور */}
                {currentStep === 3 && (
                  <div className="step-container active">
                    <h3 className="step-title">الخطوة 3: كلمة المرور</h3>
                    <input
                      type="password"
                      id="signup-password"
                      name="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required={true}
                      disabled={loading}
                      placeholder="كلمة المرور (6 أحرف على الأقل)"
                      autoComplete="new-password"
                      autoFocus
                    />
                  </div>
                )}

                {/* الخطوة الرابعة: رقم الهاتف */}
                {currentStep === 4 && (
                  <div className="step-container active">
                    <h3 className="step-title">الخطوة 4: رقم الهاتف (اختياري)</h3>
                    <input
                      type="tel"
                      id="signup-phone"
                      name="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      disabled={loading}
                      placeholder="رقم الهاتف (اختياري)"
                      autoComplete="tel"
                      autoFocus
                    />
                  </div>
                )}

                <button type="submit" disabled={loading || (currentStep < 4 && !isStepValid(currentStep))}>
                  {loading ? 'جاري المعالجة...' :
                   currentStep === 4 ? 'إنشاء حساب' : 'متابعة'}
                </button>
              </form>
            </div>
            
            <div className="button-container">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={prevStep}
                  className="prev-btn"
                  disabled={loading}
                >
                  السابق
                </button>
              )}
            </div>
            
            <div className="signup-link">
              <a href="/login" className="create-account-link">
                لديك حساب بالفعل؟ تسجيل الدخول
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SignUp;