/* Modern Centered Design - SignUp Style */
@import '../styles/variables.css';

.signup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-dark-blue) 0%, var(--primary-medium-blue) 100%);
  font-family: var(--font-family-primary);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

/* خلفية هندسية متحركة */
.signup-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 50%, rgba(218, 209, 222, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(76, 104, 192, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(218, 209, 222, 0.05) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(2deg); }
}

.signup-box {
  background: var(--white);
  padding: 50px 50px;
  border-radius: 20px;
  box-shadow:
    0 20px 60px var(--shadow-medium),
    0 10px 30px var(--shadow-light);
  width: 100%;
  max-width: 450px;
  position: relative;
  z-index: 2;
}

/* تطبيق الثيم الجديد على نموذج التسجيل */
#signup-form {
  --primary-color: orangered;
  --gray-border-dark: #b9b9b9;
  --gray-light: #f2f3f6;
  --gray: #727586;
  --dark: #262626;

  max-width: calc(100% - 40px);
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.signup-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.logo-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.legal-agenda-logo {
  width: 230px;
  height: auto;
  filter: drop-shadow(0 4px 8px var(--shadow-light));
}

/* تطبيق الثيم الجديد على العناوين */
#signup-form .header-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

#signup-form .header-form .title {
  font-size: 34px;
  font-weight: 500;
  color: var(--primary-dark-blue);
  margin-bottom: 8px;
  line-height: 1.2;
}

#signup-form .header-form .subtitle {
  color: var(--gray);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.4;
}

.logo-section .google-title {
  color: var(--primary-dark-blue);
  margin-bottom: 8px;
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1.2;
}

.logo-section .subtitle {
  color: var(--dark-blue-gray);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.4;
}

.form-section {
  width: 100%;
}

.form-content {
  margin-top: 20px;
}

/* مؤشر الخطوات */
.steps-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px 0;
}

.step-dot {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.step-dot.pending {
  background: var(--light-purple-gray);
  color: var(--dark-blue-gray);
  border: 2px solid var(--light-purple-gray);
  opacity: 0.5;
}

.step-dot.active {
  background: linear-gradient(135deg, var(--primary-medium-blue), var(--primary-dark-blue));
  color: var(--white);
  border: 2px solid var(--primary-medium-blue);
  transform: scale(1.1);
  box-shadow: 0 8px 20px var(--shadow-medium);
  animation: pulse 2s infinite;
}

.step-dot.completed {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: var(--white);
  border: 2px solid #28a745;
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

@keyframes pulse {
  0%, 100% { 
    box-shadow: 0 8px 20px var(--shadow-medium);
  }
  50% { 
    box-shadow: 0 12px 30px var(--shadow-heavy);
  }
}


/* حاوي الخطوة */
.step-container {
  min-height: 120px;
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-container.active {
  opacity: 1;
  transform: translateX(0);
}

.step-title {
  color: var(--primary-dark-blue);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  text-shadow: 0 2px 4px var(--shadow-light);
}

.form-group {
  margin-bottom: 25px;
  position: relative;
}

/* تطبيق الثيم الجديد على الحقول */
#signup-form input {
  padding: 15px 20px;
  border: 1px solid var(--gray-border-dark);
  border-radius: 5px;
  user-select: none;
  outline: none;
  transition: 0.2s ease-in-out;
  width: 100%;
  font-size: 1.1rem;
  font-family: var(--font-family-primary);
  box-sizing: border-box;
  min-height: 55px;
  background-color: var(--white);
  color: var(--primary-dark-blue);
  margin-bottom: 15px;
}

#signup-form input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(255, 69, 0, 0.1);
}

.form-group input[type='text'],
.form-group input[type='email'],
.form-group input[type='password'],
.form-group input[type='tel'] {
  width: 100%;
  padding: 20px 25px;
  border: 2px solid var(--light-purple-gray);
  border-radius: 15px;
  background-color: var(--white);
  color: var(--primary-dark-blue);
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  outline: none;
  min-height: 65px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-weight: 500;
}

.form-group input:focus {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
  transform: translateY(-2px);
}

.form-group input::placeholder {
  color: var(--dark-blue-gray);
  opacity: 0.7;
}

.form-group input:hover {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 4px 12px var(--shadow-light);
}

.form-group input:valid {
  border-color: #28a745;
}

.button-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
  flex-wrap: wrap;
}

.button-container:has(.prev-btn) {
  flex-direction: row;
}

.button-container:not(:has(.prev-btn)) {
  justify-content: center;
}

/* زر السابق */
.prev-btn {
  background: transparent;
  color: var(--primary-medium-blue);
  padding: 12px 25px;
  border: 2px solid var(--primary-medium-blue);
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 100px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.prev-btn:hover {
  background: var(--primary-medium-blue);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px var(--shadow-light);
}

.prev-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* تطبيق الثيم الجديد على الأزرار */
#signup-form button {
  border: none;
  padding: 15px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.1s ease-in-out;
  font-family: var(--font-family-primary);
  font-size: 1.1rem;
  min-height: 55px;
  width: 100%;
  margin-top: 10px;
  font-weight: 500;
}

#signup-form button[type="submit"] {
  background-color: var(--primary-color);
  color: #fff;
}

#signup-form button[type="submit"]:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

#signup-form button:disabled {
  background-color: var(--gray-light);
  color: var(--gray);
  cursor: not-allowed;
  opacity: 0.6;
}

/* تعديل زر التالي/إنشاء الحساب */
.google-login-btn {
  flex: 1;
  max-width: 200px;
}

.google-login-btn {
  background: linear-gradient(135deg, var(--primary-medium-blue) 0%, var(--primary-dark-blue) 100%);
  color: var(--white);
  padding: 18px 40px;
  border: none;
  border-radius: 15px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  min-height: 60px;
  width: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

.google-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.google-login-btn:hover::before {
  left: 100%;
}

.google-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.google-login-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px var(--shadow-medium);
}

.google-login-btn:disabled {
  background: var(--light-purple-gray);
  color: var(--dark-blue-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.google-login-btn:disabled::before {
  display: none;
}

.create-account-link {
  color: var(--primary-medium-blue);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 10px 20px;
  border-radius: 10px;
}

.create-account-link:hover {
  background-color: var(--light-purple-gray);
  color: var(--primary-dark-blue);
}

/* مجموعة رابط التسجيل */
.signup-link {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--light-purple-gray);
}

/* تأثيرات الانتقال للحقول */
.form-group input[type='text'],
.form-group input[type='email'],
.form-group input[type='password'],
.form-group input[type='tel'] {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تأثير التركيز المحسن */
.form-group input:focus {
  border-color: var(--primary-medium-blue);
  box-shadow: 0 0 0 3px rgba(76, 104, 192, 0.1);
  transform: translateY(-2px) scale(1.02);
}

/* تأثير التحقق من صحة البيانات */
.form-group input:valid:not(:placeholder-shown) {
  border-color: #28a745;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='m2.3 6.73.7-.7L4.25 4.8 6.7 2.35l.7.7-3.2 3.18z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: left 15px center;
  background-size: 16px;
  padding-left: 45px;
}

/* تحسين الاستجابة */
@media (max-width: 480px) {
  .steps-indicator {
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .step-dot {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }
  
  .step-dot:not(:last-child)::after {
    width: 20px;
    right: -15px;
  }
  
  .button-container {
    flex-direction: column;
    gap: 15px;
  }
  
  .prev-btn,
  .google-login-btn {
    width: 100%;
    max-width: none;
  }
  
  .step-title {
    font-size: 1.1rem;
    margin-bottom: 15px;
  }
}

.error-message {
  color: #e53e3e;
  margin: 20px 0;
  padding: 15px 20px;
  background: linear-gradient(135deg, #fed7d7 0%, #fec2c2 100%);
  border-radius: 12px;
  border: 1px solid #fed7d7;
  font-size: 0.95rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.1);
  animation: slideIn 0.3s ease-out;
}

.error-message::before {
  content: "⚠️";
  margin-left: 10px;
  font-size: 1.1rem;
}

.success-message {
  color: #38a169;
  margin: 20px 0;
  padding: 15px 20px;
  background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
  border-radius: 12px;
  border: 1px solid #c6f6d5;
  font-size: 0.95rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(56, 161, 105, 0.1);
  animation: slideIn 0.3s ease-out;
}

.success-message::before {
  content: "✅";
  margin-left: 10px;
  font-size: 1.1rem;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading-container {
  margin: 30px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loader {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-purple-gray);
  border-top: 4px solid var(--primary-medium-blue);
  border-radius: 50%;
  animation: modernSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes modernSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--dark-blue-gray);
  font-size: 1rem;
  font-weight: 500;
}



/* Responsive Design for Tablets */
@media (max-width: 768px) {
  .signup-container {
    padding: 16px;
  }
  
  .signup-box {
    max-width: 90%;
    padding: 40px 30px;
  }
  
  .legal-agenda-logo {
    width: 200px;
  }
  
  .logo-section .google-title {
    font-size: 1.8rem;
  }
  
  .logo-section .subtitle {
    font-size: 0.9rem;
    margin-bottom: 25px;
  }
  
  .form-group input[type='text'],
  .form-group input[type='email'],
  .form-group input[type='password'],
  .form-group input[type='tel'] {
    padding: 18px 20px;
    font-size: 1rem;
    min-height: 60px;
  }
  

}

/* Responsive Design for Mobile */
@media (max-width: 480px) {
  .signup-container {
    padding: 12px;
  }
  
  .signup-box {
    padding: 30px 25px;
    max-width: 95%;
    border-radius: 16px;
  }
  
  .legal-agenda-logo {
    width: 200px;
    margin-bottom: 20px;
  }
  
  .logo-section .google-title {
    font-size: 1.6rem;
    margin-bottom: 6px;
  }
  
  .logo-section .subtitle {
    font-size: 0.85rem;
    margin-bottom: 20px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-group input[type='text'],
  .form-group input[type='email'],
  .form-group input[type='password'],
  .form-group input[type='tel'] {
    padding: 16px 18px;
    font-size: 16px;
    min-height: 55px;
  }
  
  .button-container {
    margin-top: 20px;
    gap: 16px;
  }
  
  .google-login-btn {
    padding: 16px 20px;
    font-size: 1rem;
    min-height: 55px;
  }
  

}

/* Enhanced Focus and Interaction States */
.signup-box:focus-within {
  box-shadow: 
    0 25px 70px var(--shadow-heavy),
    0 10px 30px var(--shadow-light);
}

.google-login-btn:focus {
  outline: none;
  box-shadow: 0 8px 25px var(--shadow-medium),
              0 0 0 3px rgba(76, 104, 192, 0.3);
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(218, 209, 222, 0.1);
}

::-webkit-scrollbar-thumb {
  background: rgba(76, 104, 192, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 104, 192, 0.5);
}